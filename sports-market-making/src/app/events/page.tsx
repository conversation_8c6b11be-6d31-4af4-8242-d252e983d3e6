'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Navigation } from '@/components/Navigation'
import { apiClient, EventWithOdds } from '@/lib/api'
import { formatDateTime, getTimeUntilEvent, formatOdds } from '@/lib/utils'
import { Button } from '@/components/ui/button'

export default function EventsPage() {
  const [events, setEvents] = useState<EventWithOdds[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedSport, setSelectedSport] = useState<string>('')
  const [sports, setSports] = useState<string[]>([])

  useEffect(() => {
    loadEvents()
    loadSports()
  }, [selectedSport])

  const loadEvents = async () => {
    try {
      const eventsData = await apiClient.getEvents({
        sport: selectedSport || undefined,
        status: 'upcoming',
        limit: 50,
      })
      setEvents(eventsData)
    } catch (error) {
      console.error('Failed to load events:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadSports = async () => {
    try {
      const sportsData = await apiClient.getSports()
      setSports(sportsData)
    } catch (error) {
      console.error('Failed to load sports:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading events...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Upcoming Events</h1>
          <p className="mt-2 text-gray-600">Place bets on upcoming sports events</p>
        </div>

        {/* Filters */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-4">
            <select
              value={selectedSport}
              onChange={(e) => setSelectedSport(e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Sports</option>
              {sports.map((sport) => (
                <option key={sport} value={sport}>
                  {sport}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Events Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {events.length > 0 ? (
            events.map((event) => (
              <div key={event.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                <div className="p-6">
                  {/* Event Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{event.name}</h3>
                      <p className="text-sm text-gray-500">{event.sport} • {event.league}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">{formatDateTime(event.start_time)}</p>
                      <p className="text-xs text-blue-600 font-medium">{getTimeUntilEvent(event.start_time)}</p>
                    </div>
                  </div>

                  {/* Teams */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="font-medium">{event.home_team}</span>
                      <span className="text-gray-500">vs</span>
                      <span className="font-medium">{event.away_team}</span>
                    </div>
                  </div>

                  {/* Odds */}
                  {event.odds && (
                    <div className="mb-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <p className="text-xs text-gray-500 mb-1">Home</p>
                          <p className="font-semibold text-lg">{formatOdds(event.odds.home_odds)}</p>
                          <p className="text-xs text-gray-500">{event.home_team}</p>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <p className="text-xs text-gray-500 mb-1">Away</p>
                          <p className="font-semibold text-lg">{formatOdds(event.odds.away_odds)}</p>
                          <p className="text-xs text-gray-500">{event.away_team}</p>
                        </div>
                        {event.odds.draw_odds && (
                          <div className="col-span-2">
                            <div className="text-center p-3 bg-gray-50 rounded-lg">
                              <p className="text-xs text-gray-500 mb-1">Draw</p>
                              <p className="font-semibold text-lg">{formatOdds(event.odds.draw_odds)}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Betting Volume */}
                  {event.odds && (
                    <div className="mb-4">
                      <p className="text-xs text-gray-500 mb-2">Betting Volume</p>
                      <div className="flex space-x-4 text-xs">
                        <span>Home: ${event.odds.total_home_volume.toFixed(0)}</span>
                        <span>Away: ${event.odds.total_away_volume.toFixed(0)}</span>
                        {event.odds.draw_odds && (
                          <span>Draw: ${event.odds.total_draw_volume.toFixed(0)}</span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Action Button */}
                  <div className="flex justify-center">
                    <Link href={`/events/${event.id}`}>
                      <Button className="w-full bg-blue-600 hover:bg-blue-700">
                        Place Bet
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-2 text-center py-12">
              <div className="text-gray-500">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No events available</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {selectedSport ? `No ${selectedSport} events found.` : 'No upcoming events at the moment.'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
