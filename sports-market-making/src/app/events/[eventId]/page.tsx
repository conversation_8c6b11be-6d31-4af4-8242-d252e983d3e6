'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/AuthProvider'
import { Navigation } from '@/components/Navigation'
import { apiClient, EventWithOdds } from '@/lib/api'
import { formatDateTime, getTimeUntilEvent, formatOdds, formatCurrency, validateBetAmount } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/toaster'

export default function EventBettingPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const { toast } = useToast()
  const eventId = parseInt(params.eventId as string)

  const [event, setEvent] = useState<EventWithOdds | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedBetType, setSelectedBetType] = useState<string>('')
  const [betAmount, setBetAmount] = useState<number>(5)
  const [placing, setPlacing] = useState(false)

  useEffect(() => {
    if (eventId) {
      loadEvent()
    }
  }, [eventId])

  const loadEvent = async () => {
    try {
      const eventData = await apiClient.getEvent(eventId)
      setEvent(eventData)
    } catch (error) {
      console.error('Failed to load event:', error)
      toast({
        title: 'Error',
        description: 'Failed to load event details',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePlaceBet = async () => {
    if (!user) {
      router.push('/login')
      return
    }

    if (!selectedBetType) {
      toast({
        title: 'Error',
        description: 'Please select a bet type',
        variant: 'destructive',
      })
      return
    }

    const validation = validateBetAmount(betAmount)
    if (!validation.valid) {
      toast({
        title: 'Error',
        description: validation.error,
        variant: 'destructive',
      })
      return
    }

    if (betAmount > user.balance) {
      toast({
        title: 'Error',
        description: 'Insufficient balance',
        variant: 'destructive',
      })
      return
    }

    setPlacing(true)

    try {
      await apiClient.placeBet(eventId, selectedBetType, betAmount)
      toast({
        title: 'Success',
        description: 'Bet placed successfully!',
        variant: 'success',
      })
      router.push('/my-bets')
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to place bet',
        variant: 'destructive',
      })
    } finally {
      setPlacing(false)
    }
  }

  const getCurrentOdds = () => {
    if (!event?.odds) return 0
    switch (selectedBetType) {
      case 'home':
        return event.odds.home_odds
      case 'away':
        return event.odds.away_odds
      case 'draw':
        return event.odds.draw_odds || 0
      default:
        return 0
    }
  }

  const getPotentialPayout = () => {
    const odds = getCurrentOdds()
    return betAmount * odds
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading event...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900">Event not found</h2>
            <Button className="mt-4" onClick={() => router.push('/events')}>
              Back to Events
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Event Details */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{event.name}</h1>
                <p className="text-gray-600">{event.sport} • {event.league}</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">{formatDateTime(event.start_time)}</p>
                <p className="text-blue-600 font-medium">{getTimeUntilEvent(event.start_time)}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-8 text-center">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{event.home_team}</h3>
                <p className="text-sm text-gray-500">Home</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{event.away_team}</h3>
                <p className="text-sm text-gray-500">Away</p>
              </div>
            </div>
          </div>
        </div>

        {/* Betting Interface */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Place Your Bet</h2>

            {!user ? (
              <div className="text-center py-8">
                <p className="text-gray-600 mb-4">Please log in to place bets</p>
                <Button onClick={() => router.push('/login')}>
                  Log In
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Bet Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Select Outcome
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    <button
                      onClick={() => setSelectedBetType('home')}
                      className={`p-4 rounded-lg border-2 transition-colors ${
                        selectedBetType === 'home'
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="text-center">
                        <p className="font-medium">{event.home_team}</p>
                        <p className="text-2xl font-bold text-blue-600">
                          {event.odds ? formatOdds(event.odds.home_odds) : '-'}
                        </p>
                      </div>
                    </button>
                    
                    <button
                      onClick={() => setSelectedBetType('away')}
                      className={`p-4 rounded-lg border-2 transition-colors ${
                        selectedBetType === 'away'
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="text-center">
                        <p className="font-medium">{event.away_team}</p>
                        <p className="text-2xl font-bold text-blue-600">
                          {event.odds ? formatOdds(event.odds.away_odds) : '-'}
                        </p>
                      </div>
                    </button>

                    {event.odds?.draw_odds && (
                      <button
                        onClick={() => setSelectedBetType('draw')}
                        className={`col-span-2 p-4 rounded-lg border-2 transition-colors ${
                          selectedBetType === 'draw'
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="text-center">
                          <p className="font-medium">Draw</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatOdds(event.odds.draw_odds)}
                          </p>
                        </div>
                      </button>
                    )}
                  </div>
                </div>

                {/* Bet Amount */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bet Amount
                  </label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="number"
                      min="5"
                      max="1000"
                      step="5"
                      value={betAmount}
                      onChange={(e) => setBetAmount(Number(e.target.value))}
                      className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                    <div className="flex space-x-2">
                      {[5, 10, 25, 50, 100].map((amount) => (
                        <button
                          key={amount}
                          onClick={() => setBetAmount(amount)}
                          className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
                        >
                          ${amount}
                        </button>
                      ))}
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    Available balance: {formatCurrency(user.balance)}
                  </p>
                </div>

                {/* Bet Summary */}
                {selectedBetType && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">Bet Summary</h3>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Bet Amount:</span>
                        <span>{formatCurrency(betAmount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Odds:</span>
                        <span>{formatOdds(getCurrentOdds())}</span>
                      </div>
                      <div className="flex justify-between font-medium">
                        <span>Potential Payout:</span>
                        <span>{formatCurrency(getPotentialPayout())}</span>
                      </div>
                      <div className="flex justify-between text-green-600">
                        <span>Potential Profit:</span>
                        <span>{formatCurrency(getPotentialPayout() - betAmount)}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Place Bet Button */}
                <Button
                  onClick={handlePlaceBet}
                  disabled={!selectedBetType || placing}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  {placing ? 'Placing Bet...' : 'Place Bet'}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
