'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { Navigation } from '@/components/Navigation'
import { apiClient, BetWithEvent } from '@/lib/api'
import { formatCurrency, formatDateTime, getBetStatusColor } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function MyBetsPage() {
  const { user } = useAuth()
  const [activeBets, setActiveBets] = useState<BetWithEvent[]>([])
  const [betHistory, setBetHistory] = useState<BetWithEvent[]>([])
  const [activeTab, setActiveTab] = useState<'active' | 'history'>('active')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadBets()
    }
  }, [user])

  const loadBets = async () => {
    try {
      const [activeData, historyData] = await Promise.all([
        apiClient.getMyBets('pending,matched'),
        apiClient.getBetHistory(50),
      ])
      
      setActiveBets(activeData)
      setBetHistory(historyData)
    } catch (error) {
      console.error('Failed to load bets:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCashOut = async (betId: number) => {
    try {
      await apiClient.cashOutBet(betId)
      // Reload bets after cash out
      loadBets()
    } catch (error) {
      console.error('Failed to cash out bet:', error)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900">Please log in to view your bets</h2>
            <Link href="/login">
              <Button className="mt-4">Go to Login</Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your bets...</p>
          </div>
        </div>
      </div>
    )
  }

  const currentBets = activeTab === 'active' ? activeBets : betHistory

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Bets</h1>
          <p className="mt-2 text-gray-600">Track your betting activity and manage your bets</p>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('active')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'active'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Active Bets ({activeBets.length})
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Bet History ({betHistory.length})
              </button>
            </nav>
          </div>
        </div>

        {/* Bets List */}
        <div className="space-y-4">
          {currentBets.length > 0 ? (
            currentBets.map((bet) => (
              <div key={bet.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      {/* Event Info */}
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{bet.event.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getBetStatusColor(bet.status)}`}>
                          {bet.status.charAt(0).toUpperCase() + bet.status.slice(1)}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-500 mb-2">
                        {bet.event.sport} • {bet.event.league}
                      </p>
                      
                      <p className="text-sm text-gray-500 mb-4">
                        {formatDateTime(bet.event.start_time)}
                      </p>

                      {/* Bet Details */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Bet Type</p>
                          <p className="font-medium">
                            {bet.bet_type === 'home' ? bet.event.home_team :
                             bet.bet_type === 'away' ? bet.event.away_team :
                             'Draw'}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-500">Amount</p>
                          <p className="font-medium">{formatCurrency(bet.amount)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Odds</p>
                          <p className="font-medium">{bet.odds.toFixed(2)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Potential Payout</p>
                          <p className="font-medium text-green-600">{formatCurrency(bet.potential_payout)}</p>
                        </div>
                      </div>

                      {/* Cash Out Value */}
                      {bet.status === 'matched' && bet.cash_out_value && (
                        <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-yellow-800">Cash Out Available</p>
                              <p className="text-sm text-yellow-600">
                                Current value: {formatCurrency(bet.cash_out_value)}
                              </p>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCashOut(bet.id)}
                              className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                            >
                              Cash Out
                            </Button>
                          </div>
                        </div>
                      )}

                      {/* Bet Result */}
                      {bet.status === 'won' && (
                        <div className="mt-4 p-3 bg-green-50 rounded-lg">
                          <p className="text-sm font-medium text-green-800">
                            🎉 Congratulations! You won {formatCurrency(bet.potential_payout)}
                          </p>
                        </div>
                      )}

                      {bet.status === 'lost' && (
                        <div className="mt-4 p-3 bg-red-50 rounded-lg">
                          <p className="text-sm font-medium text-red-800">
                            Better luck next time!
                          </p>
                        </div>
                      )}

                      {bet.status === 'cashed_out' && bet.cash_out_value && (
                        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                          <p className="text-sm font-medium text-blue-800">
                            Cashed out for {formatCurrency(bet.cash_out_value)}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  {activeTab === 'active' ? 'No active bets' : 'No betting history'}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {activeTab === 'active' 
                    ? 'Start betting on upcoming events to see your active bets here.'
                    : 'Your completed bets will appear here.'}
                </p>
                <div className="mt-6">
                  <Link href="/events">
                    <Button>Browse Events</Button>
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
