'use client'

import React, { createContext, useContext, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'

interface Toast {
  id: string
  title?: string
  description?: string
  variant?: 'default' | 'destructive' | 'success'
  duration?: number
}

interface ToasterContextType {
  toast: (toast: Omit<Toast, 'id'>) => void
}

const ToasterContext = createContext<ToasterContextType | undefined>(undefined)

export function useToast() {
  const context = useContext(ToasterContext)
  if (context === undefined) {
    throw new Error('useToast must be used within a ToasterProvider')
  }
  return context
}

export function ToasterProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([])

  const toast = useCallback((newToast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    const toastWithId = { ...newToast, id }

    setToasts((prev) => [...prev, toastWithId])

    // Auto remove after duration
    setTimeout(() => {
      setToasts((prev) => prev.filter((t) => t.id !== id))
    }, newToast.duration || 5000)
  }, [])

  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((t) => t.id !== id))
  }

  return (
    <ToasterContext.Provider value={{ toast }}>
      {children}
      <div className="fixed top-0 right-0 z-50 w-full max-w-sm p-4 space-y-4">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={cn(
              'relative rounded-lg border p-4 shadow-lg transition-all',
              {
                'bg-white border-gray-200': toast.variant === 'default' || !toast.variant,
                'bg-red-50 border-red-200': toast.variant === 'destructive',
                'bg-green-50 border-green-200': toast.variant === 'success',
              }
            )}
          >
            <button
              onClick={() => removeToast(toast.id)}
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
            {toast.title && (
              <div className={cn(
                'font-semibold',
                {
                  'text-gray-900': toast.variant === 'default' || !toast.variant,
                  'text-red-900': toast.variant === 'destructive',
                  'text-green-900': toast.variant === 'success',
                }
              )}>
                {toast.title}
              </div>
            )}
            {toast.description && (
              <div className={cn(
                'text-sm mt-1',
                {
                  'text-gray-600': toast.variant === 'default' || !toast.variant,
                  'text-red-700': toast.variant === 'destructive',
                  'text-green-700': toast.variant === 'success',
                }
              )}>
                {toast.description}
              </div>
            )}
          </div>
        ))}
      </div>
    </ToasterContext.Provider>
  )
}

// Legacy export for backward compatibility
export function Toaster() {
  return null
}
