from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from datetime import datetime

from models.database import User, Event, Bet, Odds, Transaction, BetStatus, TransactionType
from models.schemas import BetCreate
from services.market_maker import MarketMaker

class BettingService:
    """Service for handling betting operations."""

    def __init__(self):
        self.market_maker = MarketMaker()
        self.min_bet_amount = 5.0
        self.max_bet_amount = 1000.0
        self.bet_increment = 5.0

    def validate_bet_amount(self, amount: float) -> bool:
        """Validate that bet amount meets requirements."""
        if amount < self.min_bet_amount or amount > self.max_bet_amount:
            return False
        # Check if amount is a multiple of bet increment
        return amount % self.bet_increment == 0

    def place_bet(self, db: Session, user: User, bet_data: BetCreate) -> Bet:
        """Place a new bet."""
        # Validate bet amount
        if not self.validate_bet_amount(bet_data.amount):
            raise ValueError(f"Bet amount must be between ${self.min_bet_amount} and ${self.max_bet_amount} in ${self.bet_increment} increments")

        # Check user balance
        if user.balance < bet_data.amount:
            raise ValueError("Insufficient balance")

        # Get event and validate it's still open for betting
        event = db.query(Event).filter(Event.id == bet_data.event_id).first()
        if not event:
            raise ValueError("Event not found")

        if event.start_time <= datetime.utcnow():
            raise ValueError("Betting is closed for this event")

        # Get current odds
        odds = db.query(Odds).filter(Odds.event_id == bet_data.event_id).first()
        if not odds:
            raise ValueError("Odds not available for this event")

        # Get odds for the specific bet type
        if bet_data.bet_type == "home":
            current_odds = odds.home_odds
        elif bet_data.bet_type == "away":
            current_odds = odds.away_odds
        elif bet_data.bet_type == "draw":
            current_odds = odds.draw_odds
            if current_odds is None:
                raise ValueError("Draw betting not available for this event")
        else:
            raise ValueError("Invalid bet type")

        # Calculate potential payout
        potential_payout = bet_data.amount * current_odds

        # Create bet
        bet = Bet(
            user_id=user.id,
            event_id=bet_data.event_id,
            bet_type=bet_data.bet_type,
            amount=bet_data.amount,
            odds=current_odds,
            potential_payout=potential_payout,
            status=BetStatus.PENDING
        )

        # Deduct amount from user balance
        user.balance -= bet_data.amount

        # Create transaction record
        transaction = Transaction(
            user_id=user.id,
            type=TransactionType.BET_PLACED,
            amount=-bet_data.amount,
            description=f"Bet placed on {event.name}",
            balance_after=user.balance
        )

        db.add(bet)
        db.add(transaction)
        db.commit()
        db.refresh(bet)

        # Update betting volume
        self._update_betting_volume(db, odds, bet_data.bet_type, bet_data.amount)

        # Try to match the bet
        self.market_maker.match_bets(db, bet)

        # Update odds based on new volume
        new_odds = self.market_maker.update_odds_based_on_volume(db, bet_data.event_id)
        if new_odds:
            odds.home_odds = new_odds["home_odds"]
            odds.away_odds = new_odds["away_odds"]
            if new_odds["draw_odds"]:
                odds.draw_odds = new_odds["draw_odds"]
            db.commit()

        return bet

    def cash_out_bet(self, db: Session, user: User, bet_id: int) -> dict:
        """Cash out a bet before the event concludes."""
        bet = db.query(Bet).filter(
            and_(Bet.id == bet_id, Bet.user_id == user.id)
        ).first()

        if not bet:
            raise ValueError("Bet not found")

        if bet.status != BetStatus.MATCHED:
            raise ValueError("Only matched bets can be cashed out")

        # Calculate cash-out value
        cash_out_value = self.market_maker.calculate_cash_out_value(db, bet)

        if cash_out_value <= 0:
            raise ValueError("Cash-out not available for this bet")

        # Update bet status
        bet.status = BetStatus.CASHED_OUT
        bet.cash_out_value = cash_out_value

        # Add cash-out amount to user balance
        user.balance += cash_out_value

        # Create transaction record
        transaction = Transaction(
            user_id=user.id,
            type=TransactionType.CASH_OUT,
            amount=cash_out_value,
            description=f"Cash out for bet #{bet.id}",
            bet_id=bet.id,
            balance_after=user.balance
        )

        db.add(transaction)
        db.commit()

        return {
            "success": True,
            "cash_out_value": cash_out_value,
            "message": f"Successfully cashed out for ${cash_out_value:.2f}"
        }

    def get_user_bets(self, db: Session, user_id: int, status: Optional[BetStatus] = None) -> List[Bet]:
        """Get all bets for a user, optionally filtered by status."""
        query = db.query(Bet).filter(Bet.user_id == user_id)

        if status:
            query = query.filter(Bet.status == status)

        return query.order_by(Bet.created_at.desc()).all()

    def get_bet_history(self, db: Session, user_id: int, limit: int = 50) -> List[Bet]:
        """Get betting history for a user."""
        return db.query(Bet).filter(Bet.user_id == user_id)\
                 .order_by(Bet.created_at.desc())\
                 .limit(limit).all()

    def settle_event_bets(self, db: Session, event_id: int, winner: str):
        """Settle all bets for a completed event."""
        event = db.query(Event).filter(Event.id == event_id).first()
        if not event:
            raise ValueError("Event not found")

        # Get all matched bets for this event
        bets = db.query(Bet).filter(
            and_(Bet.event_id == event_id, Bet.status == BetStatus.MATCHED)
        ).all()

        for bet in bets:
            user = db.query(User).filter(User.id == bet.user_id).first()

            if bet.bet_type == winner:
                # Winning bet
                bet.status = BetStatus.WON
                payout = bet.potential_payout
                user.balance += payout

                # Create transaction
                transaction = Transaction(
                    user_id=user.id,
                    type=TransactionType.BET_WON,
                    amount=payout,
                    description=f"Won bet on {event.name}",
                    bet_id=bet.id,
                    balance_after=user.balance
                )
                db.add(transaction)

            else:
                # Losing bet
                bet.status = BetStatus.LOST

                # Create transaction
                transaction = Transaction(
                    user_id=user.id,
                    type=TransactionType.BET_LOST,
                    amount=0,
                    description=f"Lost bet on {event.name}",
                    bet_id=bet.id,
                    balance_after=user.balance
                )
                db.add(transaction)

        db.commit()

    def _update_betting_volume(self, db: Session, odds: Odds, bet_type: str, amount: float):
        """Update betting volume for odds calculation."""
        if bet_type == "home":
            odds.total_home_volume += amount
        elif bet_type == "away":
            odds.total_away_volume += amount
        elif bet_type == "draw":
            odds.total_draw_volume += amount

        db.commit()
