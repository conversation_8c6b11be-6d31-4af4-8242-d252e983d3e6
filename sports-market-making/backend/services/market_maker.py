import math
from typing import Dict, <PERSON><PERSON>
from sqlalchemy.orm import Session
from models.database import Event, Odds, Bet, BetStatus

class MarketMaker:
    """
    Market making algorithm for peer-to-peer sports betting.
    Dynamically adjusts odds based on betting volume and distribution.
    """

    def __init__(self, commission_rate: float = 0.05):
        self.commission_rate = commission_rate
        self.base_margin = 0.1  # 10% base margin
        self.volume_sensitivity = 0.001  # How much volume affects odds

    def calculate_initial_odds(self, event: Event) -> Dict[str, float]:
        """
        Calculate initial odds for an event based on external factors.
        For demo purposes, we'll use simple heuristics.
        """
        # Base odds (roughly equal probability with margin)
        base_prob_home = 0.45
        base_prob_away = 0.45
        base_prob_draw = 0.10

        # Adjust based on team names (simple heuristic for demo)
        home_advantage = 0.05  # Home team gets slight advantage
        base_prob_home += home_advantage
        base_prob_away -= home_advantage / 2
        base_prob_draw -= home_advantage / 2

        # Convert probabilities to odds with margin
        margin_factor = 1 + self.base_margin

        home_odds = (1 / base_prob_home) * margin_factor
        away_odds = (1 / base_prob_away) * margin_factor
        draw_odds = (1 / base_prob_draw) * margin_factor if base_prob_draw > 0 else None

        return {
            "home_odds": round(home_odds, 2),
            "away_odds": round(away_odds, 2),
            "draw_odds": round(draw_odds, 2) if draw_odds else None
        }

    def update_odds_based_on_volume(self, db: Session, event_id: int) -> Dict[str, float]:
        """
        Update odds based on current betting volume distribution.
        """
        # Get current odds
        current_odds = db.query(Odds).filter(Odds.event_id == event_id).first()
        if not current_odds:
            return None

        # Calculate total volume for each outcome
        home_volume = current_odds.total_home_volume
        away_volume = current_odds.total_away_volume
        draw_volume = current_odds.total_draw_volume
        total_volume = home_volume + away_volume + draw_volume

        if total_volume == 0:
            return {
                "home_odds": current_odds.home_odds,
                "away_odds": current_odds.away_odds,
                "draw_odds": current_odds.draw_odds
            }

        # Calculate volume distribution
        home_ratio = home_volume / total_volume
        away_ratio = away_volume / total_volume
        draw_ratio = draw_volume / total_volume

        # Adjust odds based on volume (more volume = lower odds)
        # Use logarithmic scaling to prevent extreme odds
        volume_factor = self.volume_sensitivity * total_volume

        # Calculate new implied probabilities
        home_prob = 0.45 + (home_ratio - 0.33) * volume_factor
        away_prob = 0.45 + (away_ratio - 0.33) * volume_factor
        draw_prob = 0.10 + (draw_ratio - 0.33) * volume_factor

        # Normalize probabilities
        total_prob = home_prob + away_prob + draw_prob
        home_prob /= total_prob
        away_prob /= total_prob
        draw_prob /= total_prob

        # Convert to odds with margin
        margin_factor = 1 + self.base_margin

        new_home_odds = max(1.1, (1 / home_prob) * margin_factor)
        new_away_odds = max(1.1, (1 / away_prob) * margin_factor)
        new_draw_odds = max(1.1, (1 / draw_prob) * margin_factor) if draw_prob > 0 else None

        return {
            "home_odds": round(new_home_odds, 2),
            "away_odds": round(new_away_odds, 2),
            "draw_odds": round(new_draw_odds, 2) if new_draw_odds else None
        }

    def calculate_cash_out_value(self, db: Session, bet: Bet) -> float:
        """
        Calculate fair cash-out value for a bet based on current odds and time remaining.
        """
        if bet.status != BetStatus.MATCHED:
            return 0.0

        # Get current odds for the event
        current_odds = db.query(Odds).filter(Odds.event_id == bet.event_id).first()
        if not current_odds:
            return 0.0

        # Get current odds for the bet type
        if bet.bet_type == "home":
            current_market_odds = current_odds.home_odds
        elif bet.bet_type == "away":
            current_market_odds = current_odds.away_odds
        elif bet.bet_type == "draw":
            current_market_odds = current_odds.draw_odds
        else:
            return 0.0

        # Calculate implied probability of winning
        current_prob = 1 / current_market_odds if current_market_odds > 0 else 0
        original_prob = 1 / bet.odds if bet.odds > 0 else 0

        # Time decay factor (closer to event = less cash-out value)
        event = db.query(Event).filter(Event.id == bet.event_id).first()
        if not event:
            return 0.0

        # Simple time-based adjustment (in a real system, this would be more sophisticated)
        time_factor = 0.9  # 90% of theoretical value due to time decay

        # Calculate fair value
        if current_prob > original_prob:
            # Bet is in a better position
            improvement_factor = current_prob / original_prob
            fair_value = bet.amount * improvement_factor * time_factor
        else:
            # Bet is in a worse position
            decline_factor = current_prob / original_prob
            fair_value = bet.amount * decline_factor * time_factor

        # Apply commission
        cash_out_value = fair_value * (1 - self.commission_rate)

        return max(0, round(cash_out_value, 2))

    def match_bets(self, db: Session, new_bet: Bet) -> bool:
        """
        Attempt to match a new bet with existing opposing bets.
        For simplicity, we'll use a basic matching algorithm.
        """
        # Find opposing bets
        opposing_type = self._get_opposing_bet_type(new_bet.bet_type)
        if not opposing_type:
            return False

        # Find matching bets with compatible odds and amounts
        potential_matches = db.query(Bet).filter(
            Bet.event_id == new_bet.event_id,
            Bet.bet_type == opposing_type,
            Bet.status == BetStatus.PENDING,
            Bet.amount == new_bet.amount  # For simplicity, require exact amount match
        ).all()

        if potential_matches:
            # Match with the first compatible bet
            matched_bet = potential_matches[0]

            # Update both bets
            new_bet.status = BetStatus.MATCHED
            new_bet.matched_bet_id = matched_bet.id
            matched_bet.status = BetStatus.MATCHED
            matched_bet.matched_bet_id = new_bet.id

            db.commit()
            return True

        return False

    def _get_opposing_bet_type(self, bet_type: str) -> str:
        """Get the opposing bet type for matching."""
        if bet_type == "home":
            return "away"
        elif bet_type == "away":
            return "home"
        # For draw bets, we'd need more complex logic
        return None
