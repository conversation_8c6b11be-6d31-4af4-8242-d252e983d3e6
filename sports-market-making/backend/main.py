from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import socketio
from datetime import datetime, timedelta
import uvicorn

from models.database import create_tables, get_db, Event, Odds, User, EventStatus
from api import auth, events, betting
from services.market_maker import MarketMaker

# Create FastAPI app
app = FastAPI(
    title="Sports Betting API",
    description="Peer-to-peer sports betting platform with market making",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Next.js dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins=["http://localhost:3000"]
)

# Wrap with Socket.IO ASGI app
socket_app = socketio.ASGIApp(sio, app)

# Include API routes
app.include_router(auth.router, prefix="/api")
app.include_router(events.router, prefix="/api")
app.include_router(betting.router, prefix="/api")

@app.on_event("startup")
async def startup_event():
    """Initialize database and create sample data."""
    create_tables()
    await create_sample_data()

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Sports Betting API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

# Socket.IO events
@sio.event
async def connect(sid, environ):
    """Handle client connection."""
    print(f"Client {sid} connected")
    await sio.emit('connected', {'message': 'Connected to betting server'}, room=sid)

@sio.event
async def disconnect(sid):
    """Handle client disconnection."""
    print(f"Client {sid} disconnected")

@sio.event
async def join_event(sid, data):
    """Join a specific event room for real-time updates."""
    event_id = data.get('event_id')
    if event_id:
        await sio.enter_room(sid, f"event_{event_id}")
        await sio.emit('joined_event', {'event_id': event_id}, room=sid)

@sio.event
async def leave_event(sid, data):
    """Leave a specific event room."""
    event_id = data.get('event_id')
    if event_id:
        await sio.leave_room(sid, f"event_{event_id}")
        await sio.emit('left_event', {'event_id': event_id}, room=sid)

async def broadcast_odds_update(event_id: int, odds_data: dict):
    """Broadcast odds update to all clients watching the event."""
    await sio.emit('odds_update', {
        'event_id': event_id,
        'odds': odds_data,
        'timestamp': datetime.utcnow().isoformat()
    }, room=f"event_{event_id}")

async def create_sample_data():
    """Create sample events and users for demo purposes."""
    from sqlalchemy.orm import Session

    db = next(get_db())

    # Check if we already have data
    existing_events = db.query(Event).count()
    if existing_events > 0:
        db.close()
        return

    # Create sample users
    from services.auth import create_user

    sample_users = [
        {"email": "<EMAIL>", "username": "john_doe", "password": "password123"},
        {"email": "<EMAIL>", "username": "jane_smith", "password": "password123"},
        {"email": "<EMAIL>", "username": "bob_wilson", "password": "password123"},
        {"email": "<EMAIL>", "username": "alice_brown", "password": "password123"},
    ]

    for user_data in sample_users:
        try:
            create_user(db, user_data["email"], user_data["username"], user_data["password"])
        except:
            pass  # User might already exist

    # Create sample events
    now = datetime.utcnow()

    sample_events = [
        {
            "name": "Lakers vs Warriors",
            "sport": "Basketball",
            "league": "NBA",
            "home_team": "Los Angeles Lakers",
            "away_team": "Golden State Warriors",
            "start_time": now + timedelta(hours=2)
        },
        {
            "name": "Chiefs vs Bills",
            "sport": "Football",
            "league": "NFL",
            "home_team": "Kansas City Chiefs",
            "away_team": "Buffalo Bills",
            "start_time": now + timedelta(hours=6)
        },
        {
            "name": "Real Madrid vs Barcelona",
            "sport": "Soccer",
            "league": "La Liga",
            "home_team": "Real Madrid",
            "away_team": "Barcelona",
            "start_time": now + timedelta(days=1)
        },
        {
            "name": "Yankees vs Red Sox",
            "sport": "Baseball",
            "league": "MLB",
            "home_team": "New York Yankees",
            "away_team": "Boston Red Sox",
            "start_time": now + timedelta(hours=4)
        },
        {
            "name": "Celtics vs Heat",
            "sport": "Basketball",
            "league": "NBA",
            "home_team": "Boston Celtics",
            "away_team": "Miami Heat",
            "start_time": now + timedelta(hours=8)
        }
    ]

    market_maker = MarketMaker()

    for event_data in sample_events:
        event = Event(**event_data)
        db.add(event)
        db.commit()
        db.refresh(event)

        # Create initial odds
        initial_odds = market_maker.calculate_initial_odds(event)
        odds = Odds(
            event_id=event.id,
            home_odds=initial_odds["home_odds"],
            away_odds=initial_odds["away_odds"],
            draw_odds=initial_odds["draw_odds"] if event_data["sport"] == "Soccer" else None
        )
        db.add(odds)

    db.commit()
    db.close()
    print("Sample data created successfully!")

if __name__ == "__main__":
    uvicorn.run(
        "main:socket_app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
