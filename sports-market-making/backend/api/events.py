from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime

from models.database import get_db, Event, Odds, EventStatus
from models.schemas import Event as EventSchema, EventWithOdds, EventCreate
from services.auth import get_current_active_user
from services.market_maker import MarketMaker

router = APIRouter(prefix="/events", tags=["events"])

@router.get("/", response_model=List[EventWithOdds])
async def get_events(
    sport: Optional[str] = Query(None, description="Filter by sport"),
    league: Optional[str] = Query(None, description="Filter by league"),
    status: Optional[EventStatus] = Query(None, description="Filter by status"),
    limit: int = Query(50, le=100, description="Maximum number of events to return"),
    db: Session = Depends(get_db)
):
    """Get list of events with their current odds."""
    query = db.query(Event)

    # Apply filters
    if sport:
        query = query.filter(Event.sport.ilike(f"%{sport}%"))
    if league:
        query = query.filter(Event.league.ilike(f"%{league}%"))
    if status:
        query = query.filter(Event.status == status)

    # Get events ordered by start time
    events = query.order_by(Event.start_time).limit(limit).all()

    # Attach odds to each event
    events_with_odds = []
    for event in events:
        odds = db.query(Odds).filter(Odds.event_id == event.id).first()
        event_dict = EventSchema.from_orm(event).dict()
        event_dict["odds"] = odds
        events_with_odds.append(event_dict)

    return events_with_odds

@router.get("/{event_id}", response_model=EventWithOdds)
async def get_event(event_id: int, db: Session = Depends(get_db)):
    """Get a specific event with its odds."""
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    odds = db.query(Odds).filter(Odds.event_id == event_id).first()

    event_dict = EventSchema.from_orm(event).dict()
    event_dict["odds"] = odds

    return event_dict

@router.get("/upcoming/", response_model=List[EventWithOdds])
async def get_upcoming_events(
    hours: int = Query(24, description="Number of hours ahead to look"),
    db: Session = Depends(get_db)
):
    """Get upcoming events within the specified time frame."""
    from datetime import timedelta

    now = datetime.utcnow()
    end_time = now + timedelta(hours=hours)

    events = db.query(Event).filter(
        and_(
            Event.start_time >= now,
            Event.start_time <= end_time,
            Event.status == EventStatus.UPCOMING
        )
    ).order_by(Event.start_time).all()

    # Attach odds to each event
    events_with_odds = []
    for event in events:
        odds = db.query(Odds).filter(Odds.event_id == event.id).first()
        event_dict = EventSchema.from_orm(event).dict()
        event_dict["odds"] = odds
        events_with_odds.append(event_dict)

    return events_with_odds

@router.get("/live/", response_model=List[EventWithOdds])
async def get_live_events(db: Session = Depends(get_db)):
    """Get currently live events."""
    events = db.query(Event).filter(Event.status == EventStatus.LIVE).all()

    # Attach odds to each event
    events_with_odds = []
    for event in events:
        odds = db.query(Odds).filter(Odds.event_id == event.id).first()
        event_dict = EventSchema.from_orm(event).dict()
        event_dict["odds"] = odds
        events_with_odds.append(event_dict)

    return events_with_odds

@router.get("/sports/", response_model=List[str])
async def get_available_sports(db: Session = Depends(get_db)):
    """Get list of available sports."""
    sports = db.query(Event.sport).distinct().all()
    return [sport[0] for sport in sports]

@router.get("/leagues/", response_model=List[str])
async def get_available_leagues(
    sport: Optional[str] = Query(None, description="Filter leagues by sport"),
    db: Session = Depends(get_db)
):
    """Get list of available leagues."""
    query = db.query(Event.league).distinct()

    if sport:
        query = query.filter(Event.sport.ilike(f"%{sport}%"))

    leagues = query.all()
    return [league[0] for league in leagues]

# Admin routes (would need admin authentication in production)
@router.post("/", response_model=EventSchema)
async def create_event(
    event_data: EventCreate,
    db: Session = Depends(get_db),
    # current_user = Depends(get_current_active_user)  # Would check for admin role
):
    """Create a new event (admin only)."""
    event = Event(**event_data.dict())
    db.add(event)
    db.commit()
    db.refresh(event)

    # Create initial odds
    market_maker = MarketMaker()
    initial_odds = market_maker.calculate_initial_odds(event)

    odds = Odds(
        event_id=event.id,
        home_odds=initial_odds["home_odds"],
        away_odds=initial_odds["away_odds"],
        draw_odds=initial_odds["draw_odds"]
    )
    db.add(odds)
    db.commit()

    return event

@router.put("/{event_id}/status")
async def update_event_status(
    event_id: int,
    status: EventStatus,
    winner: Optional[str] = None,
    home_score: Optional[int] = None,
    away_score: Optional[int] = None,
    db: Session = Depends(get_db),
    # current_user = Depends(get_current_active_user)  # Would check for admin role
):
    """Update event status and scores (admin only)."""
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    event.status = status
    if home_score is not None:
        event.home_score = home_score
    if away_score is not None:
        event.away_score = away_score
    if winner:
        event.winner = winner

    db.commit()

    # If event is finished, settle bets
    if status == EventStatus.FINISHED and winner:
        from ..services.betting import BettingService
        betting_service = BettingService()
        betting_service.settle_event_bets(db, event_id, winner)

    return {"message": "Event updated successfully"}
